import React, { useState, useEffect } from 'react';
import { Link as RouterLink, useSearchParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Link,
  Grid,
  useTheme,
  IconButton,
  InputAdornment,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { motion } from 'framer-motion';
import { resetPassword } from '../api/auth';
import AnimatedBackground from '../components/ui/AnimatedBackground';
import EducationalIllustration from '../components/ui/EducationalIllustration';

// Create motion components
const MotionBox = motion(Box);
const MotionCard = motion(Card);

const ResetPasswordNew: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const theme = useTheme();
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: ''
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setMessage('Invalid or missing reset token. Please request a new password reset.');
      setIsSuccess(false);
    }
  }, [token]);

  const validatePassword = (password: string) => {
    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!/(?=.*\d)/.test(password)) {
      return 'Password must contain at least one number';
    }
    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!token) {
      setMessage('Invalid or missing reset token');
      setIsSuccess(false);
      return;
    }

    if (!formData.newPassword.trim() || !formData.confirmPassword.trim()) {
      setMessage('Please fill in all fields');
      setIsSuccess(false);
      return;
    }

    const passwordError = validatePassword(formData.newPassword);
    if (passwordError) {
      setMessage(passwordError);
      setIsSuccess(false);
      return;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      setMessage('Passwords do not match');
      setIsSuccess(false);
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const response = await resetPassword({
        token,
        new_password: formData.newPassword
      });
      
      setMessage(response.message);
      setIsSuccess(response.success);
      
      if (response.success) {
        // Redirect to login after 3 seconds
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      }
    } catch (error: any) {
      console.error('Reset password error:', error);
      if (error.response?.data?.detail) {
        setMessage(error.response.data.detail);
      } else {
        setMessage('An error occurred. Please try again.');
      }
      setIsSuccess(false);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  if (!token) {
    return (
      <AnimatedBackground>
        <Grid container sx={{ minHeight: '100vh' }}>
          <Grid
            item
            xs={12}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              p: 4,
            }}
          >
            <MotionCard
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              sx={{
                maxWidth: 400,
                width: '100%',
                boxShadow: theme.palette.mode === 'light' 
                  ? '0 10px 40px rgba(0, 0, 0, 0.1)' 
                  : '0 10px 40px rgba(0, 0, 0, 0.3)',
                borderRadius: 3,
                overflow: 'hidden',
                backdropFilter: 'blur(10px)',
                backgroundColor: theme.palette.mode === 'light'
                  ? 'rgba(255, 255, 255, 0.9)'
                  : 'rgba(18, 18, 18, 0.9)',
              }}
            >
              <CardContent sx={{ p: 4, textAlign: 'center' }}>
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 'bold',
                    color: 'error.main',
                    mb: 2,
                  }}
                >
                  Invalid Reset Link
                </Typography>
                <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                  This password reset link is invalid or has expired.
                </Typography>
                <Button
                  component={RouterLink}
                  to="/forgot-password"
                  variant="contained"
                  sx={{
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 'bold',
                  }}
                >
                  Request New Reset Link
                </Button>
              </CardContent>
            </MotionCard>
          </Grid>
        </Grid>
      </AnimatedBackground>
    );
  }

  return (
    <AnimatedBackground>
      <Grid container sx={{ minHeight: '100vh' }}>
        {/* Left side - Reset Password Form */}
        <Grid
          item
          xs={12}
          md={6}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 4,
          }}
        >
          <MotionCard
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            sx={{
              maxWidth: 400,
              width: '100%',
              boxShadow: theme.palette.mode === 'light' 
                ? '0 10px 40px rgba(0, 0, 0, 0.1)' 
                : '0 10px 40px rgba(0, 0, 0, 0.3)',
              borderRadius: 3,
              overflow: 'hidden',
              backdropFilter: 'blur(10px)',
              backgroundColor: theme.palette.mode === 'light'
                ? 'rgba(255, 255, 255, 0.9)'
                : 'rgba(18, 18, 18, 0.9)',
            }}
          >
            <CardContent sx={{ p: 4 }}>
              <MotionBox
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                sx={{ textAlign: 'center', mb: 4 }}
              >
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 'bold',
                    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    mb: 1,
                  }}
                >
                  Reset Password
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Enter your new password below
                </Typography>
              </MotionBox>

              <form onSubmit={handleSubmit}>
                <MotionBox
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  sx={{ mb: 3 }}
                >
                  <TextField
                    fullWidth
                    id="newPassword"
                    name="newPassword"
                    label="New Password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.newPassword}
                    onChange={handleInputChange}
                    disabled={loading}
                    required
                    autoComplete="new-password"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.2s',
                        '&:hover': {
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                        },
                        '&.Mui-focused': {
                          boxShadow: '0 4px 12px rgba(25, 118, 210, 0.2)',
                        },
                      },
                    }}
                  />
                </MotionBox>

                <MotionBox
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  sx={{ mb: 3 }}
                >
                  <TextField
                    fullWidth
                    id="confirmPassword"
                    name="confirmPassword"
                    label="Confirm New Password"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    disabled={loading}
                    required
                    autoComplete="new-password"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                            edge="end"
                          >
                            {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.2s',
                        '&:hover': {
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                        },
                        '&.Mui-focused': {
                          boxShadow: '0 4px 12px rgba(25, 118, 210, 0.2)',
                        },
                      },
                    }}
                  />
                </MotionBox>

                <Box sx={{ mb: 3 }}>
                  <Typography variant="caption" color="text.secondary">
                    Password requirements:
                  </Typography>
                  <Box component="ul" sx={{ mt: 1, pl: 2, fontSize: '0.75rem', color: 'text.secondary' }}>
                    <li>At least 8 characters long</li>
                    <li>Contains uppercase and lowercase letters</li>
                    <li>Contains at least one number</li>
                  </Box>
                </Box>

                {message && (
                  <MotionBox
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    sx={{ mb: 3 }}
                  >
                    <Alert 
                      severity={isSuccess ? 'success' : 'error'}
                      sx={{ borderRadius: 2 }}
                    >
                      {message}
                      {isSuccess && (
                        <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                          Redirecting to login page in 3 seconds...
                        </Typography>
                      )}
                    </Alert>
                  </MotionBox>
                )}

                <MotionBox
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  sx={{ mb: 3 }}
                >
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    disabled={loading}
                    sx={{
                      py: 1.5,
                      borderRadius: 2,
                      textTransform: 'none',
                      fontSize: '1rem',
                      fontWeight: 'bold',
                      background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 6px 20px rgba(0, 0, 0, 0.3)',
                      },
                      '&:disabled': {
                        background: theme.palette.action.disabledBackground,
                        color: theme.palette.action.disabled,
                      },
                    }}
                  >
                    {loading ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : (
                      'Reset Password'
                    )}
                  </Button>
                </MotionBox>

                <MotionBox
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  sx={{ textAlign: 'center' }}
                >
                  <Typography variant="body2" color="text.secondary">
                    <Link
                      component={RouterLink}
                      to="/login"
                      sx={{
                        fontWeight: 'bold',
                        color: 'primary.main',
                        textDecoration: 'none',
                        transition: 'all 0.2s',
                        '&:hover': {
                          color: 'secondary.main',
                          textDecoration: 'underline'
                        }
                      }}
                    >
                      Back to Login
                    </Link>
                  </Typography>
                </MotionBox>
              </form>
            </CardContent>
          </MotionCard>
        </Grid>

        {/* Right side - Educational Illustration */}
        <Grid
          item
          md={6}
          sx={{
            display: { xs: 'none', md: 'block' },
            bgcolor: theme.palette.mode === 'light'
              ? 'rgba(0, 0, 0, 0.02)'
              : 'rgba(255, 255, 255, 0.02)',
          }}
        >
          <EducationalIllustration
            title="Secure Your Account"
            subtitle="Create a strong password to protect your learning progress and personal information"
          />
        </Grid>
      </Grid>
    </AnimatedBackground>
  );
};

export default ResetPasswordNew;
