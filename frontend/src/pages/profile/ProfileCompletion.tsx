import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Paper,
  Container,
  Grid,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  CircularProgress,
  <PERSON><PERSON>,
  ListSubheader,
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { completeProfile, ProfileCompleteRequest } from '../../api/auth';
import { getSchools } from '../../api/schools';
import { getDepartmentsBySchool, Department } from '../../api/departments';
import { useQuery } from '@tanstack/react-query';

// Define the School interface directly in this file
interface School {
  id: number;
  name: string;
  description?: string;
  location?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

// Nigerian states for the state of origin dropdown
const nigerianStates = [
  'Abia', 'Adamawa', 'Akwa Ibom', 'Anambra', '<PERSON><PERSON>', 'Bayelsa', 'Benue', 'Borno',
  'Cross River', 'Delta', 'Ebonyi', 'Edo', 'Ekiti', 'Enugu', 'FCT', 'Gombe',
  'Imo', 'Jigawa', 'Kaduna', 'Kano', 'Katsina', 'Kebbi', 'Kogi', 'Kwara',
  'Lagos', 'Nasarawa', 'Niger', 'Ogun', 'Ondo', 'Osun', 'Oyo', 'Plateau',
  'Rivers', 'Sokoto', 'Taraba', 'Yobe', 'Zamfara'
];

// Define steps for the profile completion process
const steps = ['Personal Info', 'Education', 'Location', 'Confirmation'];

// Validation schemas for each step
const validationSchemas = [
  // Step 1: Personal Info
  Yup.object({
    other_name: Yup.string().optional(),
    gender: Yup.string().required('Gender is required'),
    phone_number: Yup.string().required('Phone number is required'),
  }),

  // Step 2: Education
  Yup.object({
    department: Yup.string().required('Department is required'),
    department_id: Yup.number().nullable(),
    level: Yup.string().required('Level is required'),
    institution_id: Yup.number().nullable(),
  }),

  // Step 3: Location
  Yup.object({
    date_of_birth: Yup.string().required('Date of birth is required'),
    state_of_origin: Yup.string().required('State of origin is required'),
  }),

  // Step 4: Confirmation
  Yup.object({}),
];

const ProfileCompletion: React.FC = () => {
  const navigate = useNavigate();
  const { user: authUser, refreshUser } = useAuth();
  const [activeStep, setActiveStep] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [profileComplete, setProfileComplete] = useState(false);
  const [selectedSchoolId, setSelectedSchoolId] = useState<number | null>(null);

  // Fetch schools from API
  const {
    data: schools = [],
    isLoading: isLoadingSchools,
    error: schoolsError
  } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools,
    retry: 3,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Fetch departments for selected school
  const {
    data: departments = [],
    isLoading: isLoadingDepartments,
    error: departmentsError
  } = useQuery({
    queryKey: ['departments', selectedSchoolId],
    queryFn: () => selectedSchoolId ? getDepartmentsBySchool(selectedSchoolId) : Promise.resolve([]),
    enabled: !!selectedSchoolId,
    retry: 3,
  });

  // Create a mock user for testing if authUser is not available
  const [user] = useState(authUser || {
    id: 1,
    email: '<EMAIL>',
    full_name: 'Test User',
    role: 'student',
    is_active: true,
    profile_completed: false,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  });

  // Check authentication and email verification status
  useEffect(() => {
    const token = localStorage.getItem('token');

    if (!token) {
      console.error('No authentication token found');
      setError('You must be logged in to complete your profile. Redirecting to login...');
      setTimeout(() => navigate('/login'), 2000);
      return;
    }

    if (authUser && !authUser.email_verified) {
      console.error('Email not verified');
      setError('Your email must be verified before completing your profile. Please check your email and verify your account first.');
      return;
    }

    if (authUser && authUser.profile_completed) {
      console.log('Profile already completed, redirecting to dashboard');
      navigate('/dashboard');
      return;
    }

    console.log('Authentication checks passed, user can complete profile');
  }, [authUser, navigate]);

  // Log the API data for debugging
  useEffect(() => {
    console.log('Schools from API:', schools);
    console.log('Schools error:', schoolsError);
    console.log('Total schools:', schools.length);
    console.log('Is loading schools:', isLoadingSchools);
    console.log('Selected school ID:', selectedSchoolId);
    console.log('Departments:', departments);
    console.log('Departments error:', departmentsError);
  }, [schools, schoolsError, isLoadingSchools, selectedSchoolId, departments, departmentsError]);

  // Create formik instance
  const formik = useFormik<ProfileCompleteRequest>({
    initialValues: {
      other_name: '',
      gender: 'male',
      phone_number: '',
      department: '',
      department_id: null,
      level: '100', // Default to 100 level
      date_of_birth: new Date(2000, 0, 1).toISOString().split('T')[0],
      state_of_origin: '',
      institution_id: null,
    },
    validationSchema: validationSchemas[activeStep],
    onSubmit: async (values) => {
      // If not on the last step, move to the next step
      if (activeStep < steps.length - 1) {
        handleNext();
        return;
      }

      // On the last step, submit the form
      setLoading(true);
      setError(null);

      try {
        console.log('Submitting profile data:', values);

        // If we're using a mock user, just simulate success
        if (!authUser) {
          console.log('Using mock user, simulating profile completion success');
          setProfileComplete(true);

          // Redirect to dashboard after a short delay
          setTimeout(() => {
            navigate('/dashboard');
          }, 2000);
          return;
        }

        // Prepare data for submission - ensure institution_id is properly formatted
        const submissionData = {
          ...values,
          // If institution_id is empty string or null, set it to undefined so it's omitted from the request
          institution_id: values.institution_id ? Number(values.institution_id) : undefined
        };

        // Log the submission data for debugging
        console.log('Prepared submission data:', submissionData);
        console.log('Institution ID type:', typeof submissionData.institution_id);

        try {
          await completeProfile(submissionData);
        } catch (submitError) {
          console.error('Error during profile completion API call:', submitError);

          // Try again without institution_id if that's causing issues
          if (submitError.response && submitError.response.status === 403) {
            console.log('Trying submission without institution_id...');
            const fallbackData = { ...submissionData };
            delete fallbackData.institution_id;

            console.log('Fallback submission data:', fallbackData);
            await completeProfile(fallbackData);
          } else {
            // Re-throw if it's not a 403 error
            throw submitError;
          }
        }
        await refreshUser();

        setProfileComplete(true);

        // Redirect to dashboard after a short delay
        setTimeout(() => {
          navigate('/dashboard');
        }, 2000);
      } catch (err: any) {
        console.error('Profile completion error:', err);

        if (err.response) {
          console.error('Error response:', err.response.data);
          const status = err.response.status;
          const detail = err.response.data?.detail;

          if (status === 401) {
            setError('Your session has expired. Please log in again.');
            // Clear invalid token and redirect to login
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            setTimeout(() => navigate('/login'), 2000);
          } else if (status === 403) {
            if (detail && detail.includes('Email must be verified')) {
              setError('Your email must be verified before completing your profile. Please check your email and verify your account first.');
            } else if (detail && detail.includes('Profile already completed')) {
              setError('Your profile has already been completed. Redirecting to dashboard...');
              setTimeout(() => navigate('/dashboard'), 2000);
            } else {
              setError('You do not have permission to complete this profile. This may be because your email is not verified or your account needs activation. Please contact support if this persists.');
            }
          } else if (detail) {
            setError(detail);
          } else if (typeof err.response.data === 'object') {
            // Handle validation errors
            const errorMessages = Object.entries(err.response.data)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');
            setError(`Validation error: ${errorMessages}`);
          } else {
            setError(`Error: ${err.response.status} ${err.response.statusText}`);
          }
        } else if (err.request) {
          setError('No response received from server. Please check your connection.');
        } else {
          setError(err.message || 'Failed to complete profile. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    },
  });

  // Update validation schema when step changes
  React.useEffect(() => {
    formik.setValidationSchema(validationSchemas[activeStep]);
  }, [activeStep, formik]);

  const handleNext = () => {
    // Validate current step before proceeding
    const currentSchema = validationSchemas[activeStep];
    try {
      const validFields = {};
      Object.keys(formik.values).forEach(key => {
        validFields[key] = formik.values[key];
      });

      currentSchema.validateSync(validFields, { abortEarly: false });
      setActiveStep((prevStep) => prevStep + 1);
      setError(null);
    } catch (err) {
      // Trigger validation to show errors
      Object.keys(formik.values).forEach(key => {
        formik.setFieldTouched(key, true);
      });
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  // Debug output for institutions
  console.log('Schools from API:', schools);

  // Render step content
  const getStepContent = (step: number) => {
    switch (step) {
      case 0: // Personal Info
        return (
          <Box
            key="step1"
          >
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="other_name"
                  name="other_name"
                  label="Other Name (Optional)"
                  variant="outlined"
                  value={formik.values.other_name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.other_name && Boolean(formik.errors.other_name)}
                  helperText={formik.touched.other_name && formik.errors.other_name}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl
                  fullWidth
                  error={formik.touched.gender && Boolean(formik.errors.gender)}
                >
                  <InputLabel id="gender-label" shrink={true}>Gender</InputLabel>
                  <Select
                    labelId="gender-label"
                    id="gender"
                    name="gender"
                    value={formik.values.gender}
                    label="Gender"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    displayEmpty
                  >
                    <MenuItem value="male">Male</MenuItem>
                    <MenuItem value="female">Female</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                  {formik.touched.gender && formik.errors.gender && (
                    <FormHelperText>{formik.errors.gender}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="phone_number"
                  name="phone_number"
                  label="Phone Number"
                  variant="outlined"
                  value={formik.values.phone_number}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.phone_number && Boolean(formik.errors.phone_number)}
                  helperText={formik.touched.phone_number && formik.errors.phone_number}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
            </Grid>
          </Box>
        );

      case 1: // Education
        return (
          <Box
            key="step2"
          >
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl
                  fullWidth
                  error={formik.touched.institution_id && Boolean(formik.errors.institution_id)}
                >
                  <InputLabel
                    id="institution-label"
                    shrink={true}
                  >
                    Institution
                  </InputLabel>
                  <Select
                    labelId="institution-label"
                    id="institution_id"
                    name="institution_id"
                    value={formik.values.institution_id || ''}
                    label="Institution"
                    onChange={(e) => {
                      const selectedSchoolId = e.target.value as number;
                      formik.setFieldValue('institution_id', selectedSchoolId);
                      setSelectedSchoolId(selectedSchoolId);
                      // Reset department when school changes
                      formik.setFieldValue('department', '');
                      formik.setFieldValue('department_id', null);
                      console.log('Selected institution ID:', selectedSchoolId);
                    }}
                    onBlur={formik.handleBlur}
                    displayEmpty
                    MenuProps={{
                      PaperProps: {
                        style: {
                          maxHeight: 300,
                          width: 'auto',
                        },
                      },
                    }}
                  >
                    <MenuItem value="">
                      <em>Select an institution</em>
                    </MenuItem>

                    {/* Use schools from API */}
                    {isLoadingSchools ? (
                      <MenuItem disabled>
                        <CircularProgress size={20} /> Loading institutions...
                      </MenuItem>
                    ) : schoolsError ? (
                      <MenuItem disabled>
                        Error loading institutions. Please refresh the page.
                      </MenuItem>
                    ) : schools.length === 0 ? (
                      <MenuItem disabled>
                        No institutions available. Please contact support.
                      </MenuItem>
                    ) : (
                      schools.map((school) => (
                        <MenuItem key={school.id} value={school.id}>
                          {school.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                  <FormHelperText>
                    {formik.touched.institution_id && formik.errors.institution_id
                      ? formik.errors.institution_id
                      : "Select your institution"}
                  </FormHelperText>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth error={formik.touched.department && Boolean(formik.errors.department)} required>
                  <InputLabel id="department-label" shrink={true}>Department</InputLabel>
                  <Select
                    labelId="department-label"
                    id="department"
                    name="department"
                    value={formik.values.department}
                    label="Department *"
                    onChange={(e) => {
                      const selectedDept = departments.find(d => d.name === e.target.value);
                      formik.setFieldValue('department_id', selectedDept ? selectedDept.id : null);
                      formik.setFieldValue('department', e.target.value);
                    }}
                    onBlur={formik.handleBlur}
                    displayEmpty
                    disabled={!selectedSchoolId || isLoadingDepartments}
                  >
                    <MenuItem value="">
                      <em>Select a department</em>
                    </MenuItem>

                    {!selectedSchoolId ? (
                      <MenuItem disabled>
                        Please select an institution first
                      </MenuItem>
                    ) : isLoadingDepartments ? (
                      <MenuItem disabled>
                        <CircularProgress size={20} /> Loading departments...
                      </MenuItem>
                    ) : departmentsError ? (
                      <MenuItem disabled>
                        Error loading departments. Please try again.
                      </MenuItem>
                    ) : departments.length === 0 ? (
                      <MenuItem disabled>
                        No departments found for selected institution.
                      </MenuItem>
                    ) : (
                      departments.map((dept) => (
                        <MenuItem key={dept.id} value={dept.name}>
                          {dept.name}
                        </MenuItem>
                      ))
                    )}
                  </Select>
                  {formik.touched.department && formik.errors.department && (
                    <FormHelperText>{formik.errors.department}</FormHelperText>
                  )}
                  {!formik.touched.department && !formik.errors.department && (
                    <FormHelperText>
                      {!selectedSchoolId
                        ? "Please select an institution first to see available departments."
                        : "Select your department of study."
                      }
                    </FormHelperText>
                  )}
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <FormControl
                  fullWidth
                  error={formik.touched.level && Boolean(formik.errors.level)}
                >
                  <InputLabel id="level-label" shrink={true}>Level</InputLabel>
                  <Select
                    labelId="level-label"
                    id="level"
                    name="level"
                    value={formik.values.level}
                    label="Level"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    displayEmpty
                  >
                    <MenuItem value="100">100 Level</MenuItem>
                    <MenuItem value="200">200 Level</MenuItem>
                    <MenuItem value="300">300 Level</MenuItem>
                    <MenuItem value="400">400 Level</MenuItem>
                    <MenuItem value="500">500 Level</MenuItem>
                    <MenuItem value="600">600 Level</MenuItem>
                  </Select>
                  {formik.touched.level && formik.errors.level && (
                    <FormHelperText>{formik.errors.level}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        );

      case 2: // Location
        return (
          <Box
            key="step3"
          >
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="date_of_birth"
                  name="date_of_birth"
                  label="Date of Birth"
                  type="date"
                  variant="outlined"
                  value={formik.values.date_of_birth}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.date_of_birth && Boolean(formik.errors.date_of_birth)}
                  helperText={formik.touched.date_of_birth && formik.errors.date_of_birth}
                  InputLabelProps={{
                    shrink: true,
                  }}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl
                  fullWidth
                  error={formik.touched.state_of_origin && Boolean(formik.errors.state_of_origin)}
                >
                  <InputLabel id="state-label" shrink={true}>State of Origin</InputLabel>
                  <Select
                    labelId="state-label"
                    id="state_of_origin"
                    name="state_of_origin"
                    value={formik.values.state_of_origin}
                    label="State of Origin"
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    displayEmpty
                  >
                    {nigerianStates.map((state) => (
                      <MenuItem key={state} value={state}>
                        {state}
                      </MenuItem>
                    ))}
                  </Select>
                  {formik.touched.state_of_origin && formik.errors.state_of_origin && (
                    <FormHelperText>{formik.errors.state_of_origin}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        );

      case 3: // Confirmation
        return (
          <Box
            key="step4"
          >
            <Typography variant="h6" gutterBottom>
              Confirm Your Information
            </Typography>
            <Paper elevation={0} sx={{ p: 3, bgcolor: 'background.default', borderRadius: 2, mb: 3 }}>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Full Name
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {user?.full_name}
                  </Typography>
                </Grid>
                {formik.values.other_name && (
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Other Name
                    </Typography>
                    <Typography variant="body1" gutterBottom>
                      {formik.values.other_name}
                    </Typography>
                  </Grid>
                )}
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Email
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {user?.email}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Gender
                  </Typography>
                  <Typography variant="body1" gutterBottom sx={{ textTransform: 'capitalize' }}>
                    {formik.values.gender}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Phone Number
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.phone_number}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Department
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.department}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Level
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.level}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Institution
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.institution_id
                      ? schools.find(s => s.id === formik.values.institution_id)?.name || 'Selected Institution'
                      : 'None'}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Date of Birth
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.date_of_birth}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">
                    State of Origin
                  </Typography>
                  <Typography variant="body1" gutterBottom>
                    {formik.values.state_of_origin}
                  </Typography>
                </Grid>
              </Grid>
            </Paper>
            <Typography variant="body2" color="text.secondary" paragraph>
              Please review your information above. Click "Complete Profile" to save your profile information.
            </Typography>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ borderRadius: 3, overflow: 'hidden', p: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
            Complete Your Profile
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Please provide the following information to complete your profile
          </Typography>
        </Box>

        {/* Stepper */}
        <Stepper
          activeStep={activeStep}
          alternativeLabel
          sx={{
            mb: 4,
            '& .MuiStepLabel-root .Mui-completed': {
              color: 'success.main',
            },
            '& .MuiStepLabel-root .Mui-active': {
              color: 'primary.main',
            },
          }}
        >
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {/* Error message */}
        {error && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
            {error}
          </Alert>
        )}

        {/* Profile completion success message */}
        {profileComplete ? (
          <Box
            sx={{ textAlign: 'center', py: 4 }}
          >
            <Typography variant="h5" gutterBottom>
              Profile Completed!
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              Your profile has been successfully completed. Redirecting to dashboard...
            </Typography>
            <CircularProgress size={24} sx={{ mt: 2 }} />
          </Box>
        ) : (
          <form onSubmit={formik.handleSubmit}>
            {/* Step content */}
            {getStepContent(activeStep)}

            {/* Navigation buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                variant="outlined"
                onClick={handleBack}
                disabled={activeStep === 0 || loading}
              >
                Back
              </Button>

              <Button
                variant="contained"
                color={activeStep === steps.length - 1 ? 'success' : 'primary'}
                type={activeStep === steps.length - 1 ? 'submit' : 'button'}
                onClick={activeStep === steps.length - 1 ? undefined : handleNext}
                disabled={loading}
              >
                {loading ? (
                  <CircularProgress size={24} color="inherit" />
                ) : activeStep === steps.length - 1 ? (
                  'Complete Profile'
                ) : (
                  'Next'
                )}
              </Button>
            </Box>
          </form>
        )}
      </Paper>
    </Container>
  );
};

export default ProfileCompletion;
